import { useEffect, useRef } from 'react';
import { getFormSuggestions } from '../utils/ai';
import { useToast } from './ToastContext';

interface InputMetadata {
    element: HTMLInputElement;
    label: string | null;
    isSelect: boolean;
}

export default function FormWatcher() {
    const observerRef = useRef<MutationObserver | null>(null);
    const watchedInputsRef = useRef<Map<HTMLInputElement, InputMetadata>>(new Map());
    const lastAiCallRef = useRef<number>(0);
    const { addToast, clearToasts } = useToast();

    const getInputLabel = (input: HTMLInputElement): string | null => {
        // Try to find label by 'for' attribute
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        // Try to find label by name attribute
        if (input.name) {
            const label = document.querySelector(`label[for="${input.name}"]`);
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        // Try to find closest label element
        const closestLabel = input.closest('label');
        if (closestLabel) {
            return closestLabel.textContent?.trim() || null;
        }

        // Try to find preceding label sibling
        let sibling = input.previousElementSibling;
        while (sibling) {
            if (sibling.tagName === 'LABEL') {
                return sibling.textContent?.trim() || null;
            }
            sibling = sibling.previousElementSibling;
        }

        // Try to find label in parent container
        const parent = input.parentElement;
        if (parent) {
            const label = parent.querySelector('label');
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        return null;
    };

    const handleInputChange = async (event: Event) => {
        const input = event.target as HTMLInputElement;
        const metadata = watchedInputsRef.current.get(input);
        if (!metadata) return;

        const form = getAllInputValues();

        // Check if 30 seconds have passed since last AI call
        const now = Date.now();
        if (now - lastAiCallRef.current >= 30000) {
            lastAiCallRef.current = now;
            
            try {
                const suggestions = await getFormSuggestions(form);

                clearToasts();

                // Create toast for overall assessment if there are suggestions
                // if (suggestions.suggestions.length > 0) {
                //     addToast(`Assessment: ${suggestions.overall_assessment}`);
                // }
                
                // Create toasts for high priority suggestions
                suggestions.suggestions
                    .filter(suggestion => suggestion.priority === 'high')
                    .forEach(suggestion => {
                        addToast(`${suggestion.suggestion}`);
                    });
            } catch (error) {
                console.error('Failed to get AI suggestions:', error);
                addToast('Failed to get AI suggestions');
            }
        }
    };

    const getAllInputValues = (): Record<string, string> => {
        const inputValues: Record<string, string> = {};

        inputValues["title"] = document.querySelector('h1')?.textContent?.trim() || '';
        document.querySelectorAll<HTMLElement>('.lm-instruction-field').forEach((element, i) => {
            inputValues[`additional-instructions-${i}`] = element.innerText?.trim() || '';
        });

        watchedInputsRef.current.forEach((metadata, input) => {
            if (!metadata.label) return;
            
            if (metadata.isSelect) {
                const selectContainer = input.closest('.Select');
                const selectedLabel = selectContainer?.querySelector('.Select-value-label');
                const selectedText = selectedLabel?.textContent?.trim() || '';
                inputValues[metadata.label] = selectedText;
            } else {
                inputValues[metadata.label] = input.value;
            }
        });
        
        return inputValues;
    };

    const addInputListener = (input: HTMLInputElement) => {
        if (!watchedInputsRef.current.has(input)) {
            const label = getInputLabel(input);
            const isSelect = !!(input.closest('.Select') && input.classList.contains('Select-input'));
            
            // Also check for hidden inputs that are part of Select components
            const isHiddenSelectInput = input.type === 'hidden' && !!input.closest('.Select');

            const metadata: InputMetadata = {
                element: input,
                label,
                isSelect: isSelect || isHiddenSelectInput
            };

            if (metadata.isSelect) {
                input.addEventListener('input', handleInputChange);
                input.addEventListener('change', handleInputChange);
                input.addEventListener('focus', handleInputChange);
                input.addEventListener('blur', handleInputChange);
            } else {
                input.addEventListener('change', handleInputChange);
            }
            
            watchedInputsRef.current.set(input, metadata);
        }
    };

    const removeInputListener = (input: HTMLInputElement) => {
        const metadata = watchedInputsRef.current.get(input);
        if (metadata) {
            if (metadata.isSelect) {
                input.removeEventListener('input', handleInputChange);
                input.removeEventListener('change', handleInputChange);
                input.removeEventListener('focus', handleInputChange);
                input.removeEventListener('blur', handleInputChange);
            } else {
                input.removeEventListener('change', handleInputChange);
            }
            
            watchedInputsRef.current.delete(input);
        }
    };

    const watchExistingInputs = () => {
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => addInputListener(input));
    };

    useEffect(() => {
        // Watch existing inputs
        watchExistingInputs();

        // Create mutation observer to watch for new inputs
        observerRef.current = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // Check for added nodes
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        
                        // Check if the added node is an input
                        if (element.tagName === 'INPUT') {
                            addInputListener(element as HTMLInputElement);
                        }
                        
                        // Check for inputs within the added node
                        const inputs = element.querySelectorAll('input');
                        inputs.forEach(input => addInputListener(input));
                    }
                });

                // Check for removed nodes
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        
                        // Check if the removed node is an input
                        if (element.tagName === 'INPUT') {
                            removeInputListener(element as HTMLInputElement);
                        }
                        
                        // Check for inputs within the removed node
                        const inputs = element.querySelectorAll('input');
                        inputs.forEach(input => removeInputListener(input));
                    }
                });
            });
        });

        // Start observing
        observerRef.current.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Cleanup function
        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
            
            // Remove all event listeners
            watchedInputsRef.current.forEach((metadata, input) => {
                removeInputListener(input);
            });
            watchedInputsRef.current.clear();
        };
    }, []);

    return (
        <div></div>
    );
}
