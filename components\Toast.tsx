import { browser } from 'wxt/browser';

interface ToastProps {
    message: string;
    onDismiss?: () => void;
    visible?: boolean;
}

export default function Toast({ message, onDismiss, visible = true }: ToastProps) {
    const handleDismiss = () => {
        onDismiss?.();
    };

    const handleClick = () => {
        if (message.includes('Click to open sidebar')) {
            browser.runtime.sendMessage({ type: 'OPEN_SIDEBAR' });
            onDismiss?.(); // Close the toast after clicking
        }
    };

    if (!visible) {
        return null;
    }

    return (
        <div
            data-toast-message={message}
            onClick={handleClick}
            style={{
                backgroundColor: '#1e3a8a',
                color: '#ffffff',
                padding: '12px 16px',
                borderRadius: '8px',
                display: 'flex',
                flexDirection: 'column',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                maxWidth: '400px',
                margin: '8px',
                cursor: message.includes('Click to open sidebar') ? 'pointer' : 'default',
            }}
        >
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '8px',
                }}
            >
                <h4
                    style={{
                        margin: '0',
                        fontSize: '18px',
                        fontWeight: 'bold',
                        color: '#FFFFFF',
                    }}
                >
                    Diamond Legal Assistant
                </h4>
                <button
                    onClick={handleDismiss}
                    style={{
                        background: 'none',
                        border: 'none',
                        color: '#ffffff',
                        cursor: 'pointer',
                        fontSize: '18px',
                        fontWeight: 'bold',
                        padding: '0',
                    }}
                >
                    ×
                </button>
            </div>
            <span>{message}</span>
        </div>
    );
}
