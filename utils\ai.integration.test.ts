import { describe, it, expect } from 'vitest';
import { getFormSuggestions } from './ai';

describe('getFormSuggestions - Integration Tests', () => {
  it('should return real AI suggestions for a sample intake form', async () => {
    const sampleFormData = {
      client_name: '<PERSON>',
      client_phone: '************',
      case_type: 'personal injury',
      incident_date: '2024-01-15',
      incident_description: 'Car accident at intersection'
    };

    const result = await getFormSuggestions(sampleFormData);

    // Verify the structure matches our expected interface
    expect(result).toHaveProperty('suggestions');
    expect(result).toHaveProperty('overall_assessment');
    expect(result).toHaveProperty('next_steps');

    expect(Array.isArray(result.suggestions)).toBe(true);
    expect(typeof result.overall_assessment).toBe('string');
    expect(Array.isArray(result.next_steps)).toBe(true);

    // Verify each suggestion has the required fields
    result.suggestions.forEach(suggestion => {
      expect(suggestion).toHaveProperty('field');
      expect(suggestion).toHaveProperty('suggestion');
      expect(suggestion).toHaveProperty('priority');
      expect(suggestion).toHaveProperty('type');
      
      expect(['high', 'medium', 'low']).toContain(suggestion.priority);
      expect(['missing_info', 'follow_up_question', 'legal_consideration', 'documentation_needed']).toContain(suggestion.type);
    });

    console.log('AI Response:', JSON.stringify(result, null, 2));
  }, 30000); // 30 second timeout for API call

  it('should handle minimal form data', async () => {
    const minimalFormData = {
      client_name: 'Jane Doe'
    };

    const result = await getFormSuggestions(minimalFormData);

    expect(result.suggestions.length).toBeGreaterThan(0);
    expect(result.overall_assessment).toBeTruthy();
    expect(result.next_steps.length).toBeGreaterThan(0);

    console.log('Minimal Form AI Response:', JSON.stringify(result, null, 2));
  }, 30000);
});
