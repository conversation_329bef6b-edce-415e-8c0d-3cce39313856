import ReactDOM from "react-dom/client";
import {ToastProvider} from "@/components/ToastContext.tsx";
import ToastBox from "@/components/ToastBox.tsx";
import FormWatcher from "@/components/FormWatcher.tsx";
import "@/assets/main.css";

export default defineContentScript({
    matches: ["https://app.lawmatics.com/forms/*"],
    cssInjectionMode: "ui",

    async main(ctx) {
        const ui = await createShadowRootUi(ctx, {
            name: "diamond-legal-assistant-content",
            position: "inline",
            anchor: "body",
            append: "first",
            onMount: (container) => {
                // Don't mount react app directly on <body>
                const wrapper = document.createElement("div");
                container.append(wrapper);

                const root = ReactDOM.createRoot(wrapper);

                root.render(
                    <ToastProvider>
                        <FormWatcher/>
                        <ToastBox/>
                    </ToastProvider>
                );
                return {root, wrapper};
            },
            onRemove: (elements) => {
                elements?.root.unmount();
                elements?.wrapper.remove();
            },
        });

        ui.mount();
    },
});