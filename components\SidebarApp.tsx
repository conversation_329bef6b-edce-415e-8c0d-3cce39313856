import { useEffect, useState } from 'react';
import { browser } from 'wxt/browser';

interface FormSuggestion {
    field: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    type: 'missing_info' | 'follow_up_question' | 'legal_consideration' | 'documentation_needed';
}

interface FormFeedback {
    suggestions: FormSuggestion[];
    overall_assessment: string;
}

export default function SidebarApp() {
    const [feedback, setFeedback] = useState<FormFeedback | null>(null);
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

    useEffect(() => {
        // Function to load suggestions from storage
        const loadSuggestions = async () => {
            try {
                const result = await browser.storage.local.get(['ai_suggestions', 'ai_suggestions_timestamp']);
                if (result.ai_suggestions) {
                    setFeedback(result.ai_suggestions);
                    if (result.ai_suggestions_timestamp) {
                        setLastUpdated(new Date(result.ai_suggestions_timestamp));
                    }
                    console.log('Loaded AI suggestions from storage:', result.ai_suggestions);
                }
            } catch (error) {
                console.error('Failed to load AI suggestions from storage:', error);
            }
        };

        // Load suggestions immediately
        loadSuggestions();

        // Listen for storage changes
        const handleStorageChange = (changes: any) => {
            if (changes.ai_suggestions) {
                setFeedback(changes.ai_suggestions.newValue);
                console.log('AI suggestions updated from storage:', changes.ai_suggestions.newValue);
            }
            if (changes.ai_suggestions_timestamp) {
                setLastUpdated(new Date(changes.ai_suggestions_timestamp.newValue));
            }
        };

        browser.storage.onChanged.addListener(handleStorageChange);

        // Poll for updates every 2 seconds as a fallback
        const pollInterval = setInterval(loadSuggestions, 2000);

        return () => {
            browser.storage.onChanged.removeListener(handleStorageChange);
            clearInterval(pollInterval);
        };
    }, []);

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return '#dc2626';
            case 'medium': return '#ea580c';
            case 'low': return '#16a34a';
            default: return '#6b7280';
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'missing_info': return '❗';
            case 'follow_up_question': return '❓';
            case 'legal_consideration': return '⚖️';
            case 'documentation_needed': return '📄';
            default: return '💡';
        }
    };

    return (
        <div style={{ 
            padding: '16px', 
            fontFamily: 'system-ui, -apple-system, sans-serif',
            height: '100vh',
            overflow: 'auto'
        }}>
            <div style={{ 
                marginBottom: '20px',
                borderBottom: '2px solid #1e3a8a',
                paddingBottom: '12px'
            }}>
                <h1 style={{ 
                    margin: '0 0 8px 0', 
                    fontSize: '20px', 
                    color: '#1e3a8a',
                    fontWeight: 'bold'
                }}>
                    Diamond Legal Assistant
                </h1>
                {lastUpdated && (
                    <p style={{ 
                        margin: '0', 
                        fontSize: '12px', 
                        color: '#6b7280' 
                    }}>
                        Last updated: {lastUpdated.toLocaleTimeString()}
                    </p>
                )}
            </div>

            {!feedback ? (
                <div style={{ 
                    textAlign: 'center', 
                    color: '#6b7280',
                    marginTop: '40px'
                }}>
                    <p>Waiting for form analysis...</p>
                    <p style={{ fontSize: '12px' }}>
                        Fill out at least 6 fields to get AI suggestions
                    </p>
                </div>
            ) : (
                <div>
                    {/* Overall Assessment */}
                    <div style={{ 
                        marginBottom: '20px',
                        padding: '12px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0'
                    }}>
                        <h3 style={{ 
                            margin: '0 0 8px 0', 
                            fontSize: '14px', 
                            color: '#374151',
                            fontWeight: '600'
                        }}>
                            Assessment
                        </h3>
                        <p style={{ 
                            margin: '0', 
                            fontSize: '13px', 
                            lineHeight: '1.5',
                            color: '#4b5563'
                        }}>
                            {feedback.overall_assessment}
                        </p>
                    </div>

                    {/* Suggestions */}
                    {feedback.suggestions.length > 0 && (
                        <div style={{ marginBottom: '20px' }}>
                            <h3 style={{ 
                                margin: '0 0 12px 0', 
                                fontSize: '16px', 
                                color: '#374151',
                                fontWeight: '600'
                            }}>
                                Suggestions ({feedback.suggestions.length})
                            </h3>
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                {feedback.suggestions.map((suggestion, index) => (
                                    <div 
                                        key={index}
                                        style={{ 
                                            padding: '12px',
                                            backgroundColor: '#ffffff',
                                            borderRadius: '6px',
                                            border: `2px solid ${getPriorityColor(suggestion.priority)}`,
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                        }}
                                    >
                                        <div style={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            marginBottom: '6px',
                                            gap: '6px'
                                        }}>
                                            <span style={{ fontSize: '14px' }}>
                                                {getTypeIcon(suggestion.type)}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                fontWeight: '600',
                                                color: getPriorityColor(suggestion.priority),
                                                textTransform: 'uppercase',
                                                letterSpacing: '0.5px'
                                            }}>
                                                {suggestion.priority}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                color: '#6b7280',
                                                backgroundColor: '#f3f4f6',
                                                padding: '2px 6px',
                                                borderRadius: '4px'
                                            }}>
                                                {suggestion.field}
                                            </span>
                                        </div>
                                        <p style={{ 
                                            margin: '0', 
                                            fontSize: '13px', 
                                            lineHeight: '1.4',
                                            color: '#374151'
                                        }}>
                                            {suggestion.suggestion}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
