import { useEffect, useState } from 'react';
import { browser } from 'wxt/browser';

interface FormSuggestion {
    field: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    type: 'missing_info' | 'follow_up_question' | 'legal_consideration' | 'documentation_needed';
}

interface FormFeedback {
    suggestions: FormSuggestion[];
    overall_assessment: string;
}

export default function SidebarApp() {
    const [feedback, setFeedback] = useState<FormFeedback | null>(null);
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    useEffect(() => {
        // Report that sidebar has opened
        browser.runtime.sendMessage({ type: 'SIDEBAR_OPENED' }).catch(console.error);

        // Function to load suggestions from storage
        const loadSuggestions = async () => {
            try {
                const result = await browser.storage.local.get(['ai_suggestions', 'ai_suggestions_timestamp', 'ai_loading']);
                if (result.ai_suggestions) {
                    setFeedback(result.ai_suggestions);
                    if (result.ai_suggestions_timestamp) {
                        setLastUpdated(new Date(result.ai_suggestions_timestamp));
                    }
                    console.log('Loaded AI suggestions from storage:', result.ai_suggestions);
                }
                if (result.ai_loading !== undefined) {
                    setIsLoading(result.ai_loading);
                }
            } catch (error) {
                console.error('Failed to load AI suggestions from storage:', error);
            }
        };

        // Load suggestions immediately
        loadSuggestions();

        // Listen for storage changes
        const handleStorageChange = (changes: any) => {
            if (changes.ai_suggestions) {
                setFeedback(changes.ai_suggestions.newValue);
                setIsLoading(false); // Stop loading when we get new suggestions
                console.log('AI suggestions updated from storage:', changes.ai_suggestions.newValue);
            }
            if (changes.ai_suggestions_timestamp) {
                setLastUpdated(new Date(changes.ai_suggestions_timestamp.newValue));
            }
            if (changes.ai_loading) {
                setIsLoading(changes.ai_loading.newValue);
                console.log('AI loading state changed:', changes.ai_loading.newValue);
            }
        };

        browser.storage.onChanged.addListener(handleStorageChange);

        // Poll for updates every 2 seconds as a fallback
        const pollInterval = setInterval(loadSuggestions, 2000);

        return () => {
            // Report that sidebar is closing
            browser.runtime.sendMessage({ type: 'SIDEBAR_CLOSED' }).catch(console.error);

            browser.storage.onChanged.removeListener(handleStorageChange);
            clearInterval(pollInterval);
        };
    }, []);

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return '#dc2626';
            case 'medium': return '#ea580c';
            case 'low': return '#16a34a';
            default: return '#6b7280';
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'missing_info': return '❗';
            case 'follow_up_question': return '❓';
            case 'legal_consideration': return '⚖️';
            case 'documentation_needed': return '📄';
            default: return '💡';
        }
    };

    return (
        <div style={{ 
            padding: '16px', 
            fontFamily: 'system-ui, -apple-system, sans-serif',
            height: '100vh',
            overflow: 'auto'
        }}>
            <div style={{
                marginBottom: '20px',
                borderBottom: '2px solid #1e3a8a',
                paddingBottom: '12px'
            }}>
                <h1 style={{
                    margin: '0 0 8px 0',
                    fontSize: '20px',
                    color: '#1e3a8a',
                    fontWeight: 'bold'
                }}>
                    Diamond Legal Assistant
                </h1>
                {lastUpdated && !isLoading && (
                    <p style={{
                        margin: '0',
                        fontSize: '12px',
                        color: '#6b7280'
                    }}>
                        Last updated: {lastUpdated.toLocaleTimeString()}
                    </p>
                )}
                {isLoading && (
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        margin: '0',
                        fontSize: '12px',
                        color: '#1e3a8a',
                        fontWeight: '500'
                    }}>
                        <div style={{
                            width: '12px',
                            height: '12px',
                            border: '2px solid #e5e7eb',
                            borderTop: '2px solid #1e3a8a',
                            borderRadius: '50%',
                            animation: 'spin 1s linear infinite'
                        }}></div>
                        Refreshing recommendations...
                    </div>
                )}
            </div>

            <style>
                {`
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `}
            </style>

            {!feedback ? (
                <div style={{ 
                    textAlign: 'center', 
                    color: '#6b7280',
                    marginTop: '40px'
                }}>
                    <p>Waiting for form analysis...</p>
                    <p style={{ fontSize: '12px' }}>
                        Fill out at least 6 fields to get AI suggestions
                    </p>
                </div>
            ) : (
                <div>
                    {/* Overall Assessment */}
                    <div style={{ 
                        marginBottom: '20px',
                        padding: '12px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0'
                    }}>
                        <h3 style={{ 
                            margin: '0 0 8px 0', 
                            fontSize: '14px', 
                            color: '#374151',
                            fontWeight: '600'
                        }}>
                            Assessment
                        </h3>
                        <p style={{ 
                            margin: '0', 
                            fontSize: '13px', 
                            lineHeight: '1.5',
                            color: '#4b5563'
                        }}>
                            {feedback.overall_assessment}
                        </p>
                    </div>

                    {/* Suggestions */}
                    {feedback.suggestions.length > 0 && (
                        <div style={{ marginBottom: '20px' }}>
                            <h3 style={{ 
                                margin: '0 0 12px 0', 
                                fontSize: '16px', 
                                color: '#374151',
                                fontWeight: '600'
                            }}>
                                Suggestions ({feedback.suggestions.length})
                            </h3>
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                {feedback.suggestions.map((suggestion, index) => (
                                    <div 
                                        key={index}
                                        style={{ 
                                            padding: '12px',
                                            backgroundColor: '#ffffff',
                                            borderRadius: '6px',
                                            border: `2px solid ${getPriorityColor(suggestion.priority)}`,
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                        }}
                                    >
                                        <div style={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            marginBottom: '6px',
                                            gap: '6px'
                                        }}>
                                            <span style={{ fontSize: '14px' }}>
                                                {getTypeIcon(suggestion.type)}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                fontWeight: '600',
                                                color: getPriorityColor(suggestion.priority),
                                                textTransform: 'uppercase',
                                                letterSpacing: '0.5px'
                                            }}>
                                                {suggestion.priority}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                color: '#6b7280',
                                                backgroundColor: '#f3f4f6',
                                                padding: '2px 6px',
                                                borderRadius: '4px'
                                            }}>
                                                {suggestion.field}
                                            </span>
                                        </div>
                                        <p style={{ 
                                            margin: '0', 
                                            fontSize: '13px', 
                                            lineHeight: '1.4',
                                            color: '#374151'
                                        }}>
                                            {suggestion.suggestion}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
