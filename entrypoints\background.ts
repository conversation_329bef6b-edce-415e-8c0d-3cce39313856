import { browser } from 'wxt/browser';

export default defineBackground(() => {
    // Handle messages from content script
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'OPEN_SIDEBAR') {
            // Open the sidebar for the current tab
            if (sender.tab?.id) {
                browser.sidePanel?.open({ tabId: sender.tab.id }).catch(console.error);
            }
        }
    });
});
