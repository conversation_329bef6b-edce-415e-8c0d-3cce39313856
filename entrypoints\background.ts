import { browser } from 'wxt/browser';

export default defineBackground(() => {
    // Handle messages from content script and forward to sidebar
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'AI_SUGGESTIONS') {
            // Forward the message to all sidebar instances
            browser.runtime.sendMessage(message).catch(() => {
                // Ignore errors if sidebar is not open
            });
        }
        
        if (message.type === 'CHECK_SIDEBAR_OPEN') {
            // Check if sidebar is open by trying to query the sidepanel
            browser.sidePanel?.getOptions({ tabId: sender.tab?.id })
                .then(() => sendResponse({ isOpen: true }))
                .catch(() => sendResponse({ isOpen: false }));
            return true; // Keep the message channel open for async response
        }
        
        if (message.type === 'OPEN_SIDEBAR') {
            // Open the sidebar for the current tab
            if (sender.tab?.id) {
                browser.sidePanel?.open({ tabId: sender.tab.id }).catch(console.error);
            }
        }
    });
});
