import { browser } from 'wxt/browser';

export default defineBackground(() => {
    // Track sidebar state per tab
    const sidebarState = new Map<number, boolean>();

    // Handle messages from content script and sidebar
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'OPEN_SIDEBAR') {
            // Open the sidebar for the current tab
            if (sender.tab?.id) {
                browser.sidePanel?.open({ tabId: sender.tab.id }).then(() => {
                    sidebarState.set(sender.tab!.id, true);
                }).catch(console.error);
            }
        } else if (message.type === 'CHECK_SIDEBAR_STATE') {
            // Return the current sidebar state for the tab
            const tabId = sender.tab?.id;
            const isOpen = tabId ? sidebarState.get(tabId) || false : false;
            sendResponse({ isOpen });
            return true; // Keep the message channel open for async response
        } else if (message.type === 'SIDEBAR_OPENED') {
            // Sidebar reports it has opened
            if (sender.tab?.id) {
                sidebarState.set(sender.tab.id, true);
            }
        } else if (message.type === 'SIDEBAR_CLOSED') {
            // Sidebar reports it has closed
            if (sender.tab?.id) {
                sidebarState.set(sender.tab.id, false);
            }
        }
    });

    // Clean up state when tabs are closed
    browser.tabs?.onRemoved?.addListener((tabId) => {
        sidebarState.delete(tabId);
    });
});
