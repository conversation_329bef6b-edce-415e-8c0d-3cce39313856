import {OpenAI} from "openai";

const openrouter = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: '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',
    dangerouslyAllowBrowser: true
});

interface FormSuggestion {
    field: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    type: 'missing_info' | 'follow_up_question' | 'legal_consideration' | 'documentation_needed';
}

interface FormFeedback {
    suggestions: FormSuggestion[];
    overall_assessment: string;
}

export async function getFormSuggestions(form: Record<string, string>): Promise<FormFeedback> {
    // Convert form to array of entries to maintain order
    const formEntries = Object.entries(form);

    // Count filled fields (excluding instruction fields)
    const filledFieldsCount = formEntries.filter(([key, value]) => {
        // Skip instruction fields (they typically have encoded names or contain HTML)
        const isInstructionField = key.includes('instruction') ||
                                 key.includes('Advanced-instructions') ||
                                 value.includes('<ul') ||
                                 value.includes('<li') ||
                                 value.includes('<p');
        return !isInstructionField && value && value.trim() !== '';
    }).length;

    // Don't send to Gemini until at least 6 fields are filled
    if (filledFieldsCount < 6) {
        console.log(`Only ${filledFieldsCount} fields filled. Waiting for at least 6 before sending to Gemini.`);
        return {
            suggestions: [],
            overall_assessment: "Form is still being filled out. Continue gathering information."
        };
    }

    // Find the index of the last field that has been filled out
    let lastFilledIndex = -1;
    for (let i = formEntries.length - 1; i >= 0; i--) {
        const [key, value] = formEntries[i];
        // Skip instruction fields when determining progress
        const isInstructionField = key.includes('instruction') ||
                                 key.includes('Advanced-instructions') ||
                                 value.includes('<ul') ||
                                 value.includes('<li') ||
                                 value.includes('<p');
        if (!isInstructionField && value && value.trim() !== '') {
            lastFilledIndex = i;
            break;
        }
    }

    // Generate form data based on progress
    let formData: string;
    if (lastFilledIndex === -1) {
        // If no fields are filled, include only the first non-instruction field
        const firstNonInstructionEntry = formEntries.find(([key, value]) => {
            const isInstructionField = key.includes('instruction') ||
                                     key.includes('Advanced-instructions') ||
                                     value.includes('<ul') ||
                                     value.includes('<li') ||
                                     value.includes('<p');
            return !isInstructionField;
        });
        formData = firstNonInstructionEntry ? `${firstNonInstructionEntry[0]}: ${firstNonInstructionEntry[1] || '(empty)'}` : '';
    } else {
        // Include all fields up to and including the last filled field
        const relevantEntries = formEntries.slice(0, lastFilledIndex + 1);
        formData = relevantEntries
            .map(([key, value]) => {
                // Format instruction fields differently
                const isInstructionField = key.includes('instruction') ||
                                         key.includes('Advanced-instructions') ||
                                         value.includes('<ul') ||
                                         value.includes('<li') ||
                                         value.includes('<p');

                if (isInstructionField) {
                    // Extract text content from HTML and format as instruction
                    const textContent = value.replace(/<[^>]*>/g, ' ')
                                           .replace(/&nbsp;/g, ' ')
                                           .replace(/&quot;/g, '"')
                                           .replace(/&amp;/g, '&')
                                           .replace(/\s+/g, ' ')
                                           .trim();
                    return `[INSTRUCTION]: ${textContent}`;
                } else {
                    return `${key}: ${value || '(empty)'}`;
                }
            })
            .join('\n');
    }

    // Log the form data being sent to Gemini for debugging
    console.log('Form data sent to Gemini:', formData);
    console.log(`Filled fields count: ${filledFieldsCount}`);

    const currentDateTime = new Date().toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short'
    });

    const response = await openrouter.chat.completions.create({
        model: "google/gemini-2.5-flash-preview-05-20",
        messages: [
            {
                role: "system",
                content: `You are an AI assistant helping a legal representative during a client intake call.

                Current date and time: ${currentDateTime}

                Analyze the partially filled intake form and provide actionable suggestions to help the rep gather complete and accurate information. Focus on:

                1. Missing critical information that should be collected
                2. Follow-up questions to clarify or expand on provided information
                3. Important legal considerations or red flags
                4. Documentation that may be needed

                Be concise and practical - the rep is on a live call and needs quick, actionable guidance.
                
                The user will send you the current intake form data, which is a list of questions and answers that have been covered in the form SO FAR. The form may ask many of the questions that you would suggest asking later. Do not suggest collecting information that the form fields or instructions from the form don't specifically request. The form is the source of truth for what needs to be collected. Keep
                
                The following is a complete list of the types of problems to look for. 
                
                <problems_to_look_for>
                I. Completeness & Accuracy of Information Rule 1.1 (Mandatory Fields): All mandatory fields (as defined by the intake form/campaign) must be fully completed. No essential data points (e.g., names, DOB, contact info, key dates) should be missing or marked 'NA' unless explicitly permitted and justified in notes. Severity: High (Prevents processing, requires follow-up) Example Issues: Missing father's phone number, missing emergency contact, missing street address, missing PCP details, missing policy/claim number, missing specific doctor name. Rule 1.2 (Specificity - Names): All names (client, child, parents, doctors, contacts, corroborators, abusers) must be full names where requested. Avoid initials or vague descriptions. Verify spelling against provided documents (ID/BC - see Section V). Severity: Medium (Causes confusion, potential identity issues) Example Issues: Asking for father's name, asking for corroborator's name, asking for friend's name, asking for therapist's name, parent names needed, missing OBGYN name. Rule 1.3 (Specificity - Addresses): Addresses must be complete (Street, City, State, Zip). Vague locations or missing components are unacceptable. For specific campaigns (e.g., SSS Maryland), the required address (e.g., "address in Maryland lived at before facility") must be provided accurately. Severity: Medium-High (Prevents record requests, impacts jurisdiction/qualification) Example Issues: Missing MD address before facility, needs full address/zip, incorrect address type requested. Rule 1.4 (Specificity - Dates): Where a timeframe or specific date is needed (e.g., "when did you tell them?", "dates seen by PCP"), provide the date/range (MM/YYYY or specific date if known). Avoid vague answers like "when released" without context. Abuse dates must be approximate years or specific date range, not just a single date if multiple instances occurred over time. Severity: Medium-High (Impacts SOL, corroboration, record requests) Example Issues: Needs date told, needs PCP date range, needs abuse start/end dates. Rule 1.5 (Specificity - 'Other' Selections): If an "Other" option is selected in a multiple-choice question, a clear explanation must be provided in the designated notes section or field. Severity: Medium (Leaves ambiguity, requires clarification) Example Issues: 'Other' medication needs explanation, 'Other' side effects needs explanation, 'Other' treatment needs explanation, 'Other' injury needs explanation, 'Other' symptom needs explanation. Rule 1.6 (Specificity - Medications/Treatments): Specific medication names are required, not just the type (e.g., "Oxycontin" not just "Pain Medication", specific steroid name not just "steroid"). Vague answers like "Does not remember" are acceptable only if explicitly stated by the client after probing. Severity: Medium (Essential for case details, medical records) Example Issues: Needs specific steroid name, needs specific pain med name, needs specific narcotic name, Hydrocodone spelling check. Rule 1.7 (Specificity - Corroboration): Stating "Yes" to corroboration is insufficient. The type of corroboration (e.g., specific document type, named family member) must be listed. If a person, ensure they are contactable (unless specified otherwise). Severity: Medium (Impacts case strength assessment) Example Issues: Needs type of corroboration, needs details if father can't be contacted, needs clarification on doctor as corroborator, needs clarification on document type. II. Date Logic & Consistency Rule 2.1 (Medical Sequence - Test/Diagnosis): Date of Diagnosis must be on or after the Date of Tests. Same-day testing and diagnosis is highly improbable for complex conditions (like tumors requiring biopsy) and requires verification or correction. Severity: High (Indicates data error, potentially fraudulent) Example Issues: Test/Diagnosis same day, Diagnosis before testing, Biopsy result timeline too short. Rule 2.2 (Medical Sequence - Biopsy Timing): Biopsy date cannot precede general testing dates related to the same diagnostic process. Biopsy results require plausible time (days, not hours) for pathology. Severity: High (Indicates data error or misunderstanding) Example Issues: Biopsy before testing, Biopsy/Test/Diagnosis dates too close. Rule 2.3 (Medical Sequence - Admission/Release): Date of Admission must be on or before Date Released for the same hospital stay. Severity: High (Clear data entry error) Example Issues: Admission after release, Release before admission. Rule 2.4 (Medical Sequence - Admission/Diagnosis): While possible, admission occurring before a definitive diagnosis warrants scrutiny, especially if the dates are far apart or seem illogical in context. Admission significantly after diagnosis also might need clarification. Admission same day as diagnosis might be possible but can be a flag combined with other issues. Severity: Medium (Requires context check, potential flag) Example Issues: Admission before diagnosis, Admission same day as diagnosis. Rule 2.5 (Age/Event Alignment): Client's stated age at the time of an event (e.g., abuse, Depo use) must logically align with their Date of Birth and the stated dates of the event. Severity: High (Impacts qualification, SOL, credibility) Example Issues: Age 15 but abuse date implies age 24, Abuse dates imply age 25. Rule 2.6 (Duration Consistency): Calculated durations must match start/end dates provided (e.g., Depo use duration, formula feeding days, PCP visit range). Severity: Medium (Data inconsistency) Example Issues: Formula days calculation mismatch. Depo use must be >= 1 year for diagnosis timing check (numerous Depo examples imply this check). Rule 2.7 (Date Range vs. Frequency): If an event (e.g., abuse) occurred multiple times over a period, the "Dates of abuse" should reflect a range (e.g., MM/YYYY - MM/YYYY or Approx. Year - Approx. Year), not just a single date or month, unless the multiple instances all occurred within that very specific, short timeframe. Severity: Medium (Accuracy of event timeline) Example Issues: Abuse 3 times but dates only show one month, Abuse 25 times on one specific date?, Abuse 7-8 times in one specific month?, Abuse 5-10 times in one specific month?, Abuse 2 times on one specific date?, Abuse 4-5 times on one specific date?, Abuse 2-3x/week for a year but date listed is single day. III. Consistency, Credibility & Plausibility Rule 3.1 (Internal Consistency - Fields vs. Notes): Information entered in structured fields must not contradict information detailed in free-text case notes. Severity: Medium-High (Indicates carelessness or conflicting information) Example Issues: Notes say told people, field says 'No', Notes detail PCP info, fields say 'NA', Notes mention Urgent Care, needs to be moved to field, Abuse description should be in field not notes. Rule 3.2 (Narrative Plausibility - Age/Context): The overall narrative must be logical. Client's age should align with context (e.g., attending elementary school at 15 is highly unlikely). Reasons for being in a facility should align with stated events. Severity: Medium-High (Credibility flag) Example Issues: 15yo in elementary school, 13yo in high school (noted as less likely but confirmed by rep), Reason for facility vs. crime committed. Rule 3.3 (Narrative Plausibility - Medical): The described medical condition, symptoms, and treatments should generally align. Major discrepancies require clarification (e.g., Exploratory Laparotomy for Lung Issues or Poor Growth alone). Severity: Medium-High (Credibility/Accuracy flag) Example Issues: Surgery reason unclear for NEC stated injuries, Poor growth requiring surgery. Rule 3.4 (Behavioral Red Flags): Note any observations during the call suggesting potential coaching, reading from a script, excessive delays, getting answers via text, evasiveness, or significant memory gaps for recent/major events. While not definitive proof of inaccuracy, these warrant closer scrutiny of the data. Severity: Medium (Credibility flag) Example Issues: Reading script/paper, Getting answers via text, Evasive/Inconsistent, Background noise/distractions, Flipping pages, Confused dates/memory loss claims for recent events. Rule 3.5 (Facility Type Consistency): Stated facility name/type should align with known information (e.g., client gender matching a gender-specific facility). Verify if questionable. Severity: High (Potential DQ or significant error) Example Issues: Male-only facility mentioned for female client. IV. Procedural Adherence & Documentation Rule 4.1 (Correct Retainer/Workflow): Ensure the correct retainer type/version is selected and sent, matching the specific case type and campaign requirements (e.g., SSS-PLG, KP MLF RP 60-40). Follow specified transfer protocols if applicable. Severity: High (Prevents case progression, legal requirement) Example Issues: Wrong retainer type, Needs specific retainer sent, Needs new intake/process followed. Rule 4.2 (Valid Signature): Signatures on retainers must be actual signatures (digital or wet). Printed names, initials, or clearly auto-generated/font-based signatures are unacceptable and require a re-sign. Severity: High (Legal requirement) Example Issues: Unacceptable signature/needs resign. Rule 4.3 (Document Format): Submitted documents (ID, BC) must meet specified requirements (e.g., clear photo, client holding the document in the photo). Scans or photos not meeting criteria need correction. Severity: Medium-High (Verification requirement) Example Issues: ID/BC needs to be held, BC needs to be actual cert/better quality. Rule 4.4 (Information Placement): Enter data into the correct, designated fields on the intake form. Do not leave critical data only in the general case notes section if a specific field exists for it. Severity: Medium (Data organization, downstream processing) Example Issues: Move abuse description to correct field, Move PCP info from notes to fields. Rule 4.5 (Note Cleanliness): Remove extraneous notes not relevant to the case details (e.g., vendor communication, internal process notes unless required). Severity: Low (Readability, professionalism) Example Issues: Remove vendor notes. V. Data Verification & External Consistency Rule 5.1 (Name Spelling Verification): Cross-reference client/child name spelling provided in the intake with spelling on submitted ID and Birth Certificate. Discrepancies must be clarified and corrected. Severity: Medium-High (Legal identity, record requests) Example Issues: Last name discrepancy ID vs. Intake, Father's name spelling BC vs. Intake, Child's name spelling BC vs. Intake. Client name spelling correction requested. Rule 5.2 (Doctor Specialty/Affiliation Check): Check if the stated doctor's specialty is logical for the action described (e.g., a Neurosurgeon prescribing Depo is unlikely, a Gyno performing brain surgery is impossible, an Oncologist diagnosing infant NEC is unusual). Verify doctor/hospital affiliations if they seem incorrect or inconsistent. Severity: Medium-High (Credibility, accuracy of records needed) Example Issues: Neurosurgeon prescribing Depo, Gyno diagnosing/doing brain surgery, Surgical Oncologist diagnosing NEC, Hematologist as PCP, Doctor not affiliated with hospital listed, Neurologist not neurosurgeon. Rule 5.3 (Insurance Status Plausibility): Claims of having no health insurance during major medical events (like childbirth with complications) may warrant a note or secondary check for plausibility, especially if significant treatment was received. Severity: Low-Medium (Context, potential billing questions) Example Issues: No insurance claim questioned. VI. Qualification Criteria (DNQ) Rule 6.1 (Timing Criteria): Verify that key event timing meets campaign requirements (e.g., NEC diagnosed before baby discharged home, Depo use > 1 year before diagnosis). Severity: Critical (Disqualification) Example Issues: Baby home before NEC diagnosis, Depo use duration potentially too short (implied check in many Depo cases). Rule 6.2 (Product/Event Criteria): Confirm the specific product (e.g., Brand Name Depo, not generic; specific formula type, not just fortifier) or nature of the event (e.g., actual sexual abuse, not just fondling/attempt) meets requirements. Severity: Critical (Disqualification) Example Issues: Generic Depo only, Fortifier only, No sexual abuse/Fondling only, Touched inappropriately only. Rule 6.3 (Disqualifying Status/History): Check for any automatic disqualifiers mentioned (e.g., registered sex offender status, prior settlement). Severity: Critical (Disqualification) Example Issues: Registered sex offender, Settled with insurance. VII. Clarity & Understanding Rule 7.1 (Complete Answers): Ensure answers are complete thoughts/sentences, especially in descriptive fields. Avoid sentence fragments. Severity: Low-Medium (Readability, clarity) Example Issues: Unfinished sentence. Rule 7.2 (Clarify Vague Language): Probe for more specific details when answers are overly vague or ambiguous (e.g., "feeling her body parts" needs more detail on how). Severity: Medium (Essential case details) Example Issues: Needs detail on sexual assault description, Needs detail on abuse description. Rule 7.3 (Confirm Understanding): Ensure the client understood questions, particularly nuanced ones like "When did you realize NEC may have resulted from formula use?" (asking about causal link awareness, not symptom onset) or the meaning of "corroboration." Severity: Medium (Accuracy of key data points) Example Issues: Misunderstanding 'when realized NEC caused by formula', Misunderstanding 'corroboration'.
                <\problems_to_look_for>`
            },
            {
                role: "user",
                content: `Current intake form data:
                
${formData}

Please analyze this intake information and provide suggestions for the legal representative. Do not recommend filling out fields that are going to be filled out later. The form is currently being worked on, so pay attention to the order of questions presented and avoid asking the user to fill out fields that they haven't reached yet. Only make a recommendation once fields that come after the field in question have been filled out.`
            }
        ],
        response_format: {
            type: "json_schema",
            json_schema: {
                name: "form_feedback",
                schema: {
                    type: "object",
                    properties: {
                        suggestions: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    field: {
                                        type: "string",
                                        description: "The form field this suggestion relates to"
                                    },
                                    suggestion: {
                                        type: "string",
                                        description: "The specific suggestion or question to ask"
                                    },
                                    priority: {
                                        type: "string",
                                        enum: ["high", "medium", "low"],
                                        description: "Priority level of this suggestion"
                                    },
                                    type: {
                                        type: "string",
                                        enum: ["missing_info", "follow_up_question", "legal_consideration", "documentation_needed"],
                                        description: "Type of suggestion"
                                    }
                                },
                                required: ["field", "suggestion", "priority", "type"]
                            }
                        },
                        overall_assessment: {
                            type: "string",
                            description: "Brief overall assessment of the intake progress"
                        }
                    },
                    required: ["suggestions", "overall_assessment"]
                }
            }
        },
        temperature: 0.3
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
        throw new Error('No response from AI');
    }

    return JSON.parse(content) as FormFeedback;
}
