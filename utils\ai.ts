import {OpenAI} from "openai";

const openrouter = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: '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',
    dangerouslyAllowBrowser: true
});

interface FormSuggestion {
    field: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    type: 'missing_info' | 'follow_up_question' | 'legal_consideration' | 'documentation_needed';
}

interface FormFeedback {
    suggestions: FormSuggestion[];
    overall_assessment: string;
    next_steps: string[];
}

export async function getFormSuggestions(form: Record<string, string>): Promise<FormFeedback> {
    const formData = Object.entries(form)
        .filter(([key, value]) => value && value.trim() !== '')
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');

    const response = await openrouter.chat.completions.create({
        model: "google/gemini-2.5-pro-preview",
        messages: [
            {
                role: "system",
                content: `You are an AI assistant helping a legal representative during a client intake call. 
                
                Analyze the partially filled intake form and provide actionable suggestions to help the rep gather complete and accurate information. Focus on:
                
                1. Missing critical information that should be collected
                2. Follow-up questions to clarify or expand on provided information
                3. Important legal considerations or red flags
                4. Documentation that may be needed
                
                Be concise and practical - the rep is on a live call and needs quick, actionable guidance.`
            },
            {
                role: "user",
                content: `Current intake form data:
                
${formData}

Please analyze this intake information and provide suggestions for the legal representative.`
            }
        ],
        response_format: {
            type: "json_schema",
            json_schema: {
                name: "form_feedback",
                schema: {
                    type: "object",
                    properties: {
                        suggestions: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    field: {
                                        type: "string",
                                        description: "The form field this suggestion relates to"
                                    },
                                    suggestion: {
                                        type: "string",
                                        description: "The specific suggestion or question to ask"
                                    },
                                    priority: {
                                        type: "string",
                                        enum: ["high", "medium", "low"],
                                        description: "Priority level of this suggestion"
                                    },
                                    type: {
                                        type: "string",
                                        enum: ["missing_info", "follow_up_question", "legal_consideration", "documentation_needed"],
                                        description: "Type of suggestion"
                                    }
                                },
                                required: ["field", "suggestion", "priority", "type"]
                            }
                        },
                        overall_assessment: {
                            type: "string",
                            description: "Brief overall assessment of the intake progress"
                        },
                        next_steps: {
                            type: "array",
                            items: {
                                type: "string"
                            },
                            description: "Recommended next steps for the rep"
                        }
                    },
                    required: ["suggestions", "overall_assessment", "next_steps"]
                }
            }
        },
        temperature: 0.3
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
        throw new Error('No response from AI');
    }

    return JSON.parse(content) as FormFeedback;
}
