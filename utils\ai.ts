import {OpenAI} from "openai";

const openrouter = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: '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',
    dangerouslyAllowBrowser: true
});

interface FormSuggestion {
    field: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    type: 'missing_info' | 'follow_up_question' | 'legal_consideration' | 'documentation_needed';
}

interface FormFeedback {
    suggestions: FormSuggestion[];
    overall_assessment: string;
    next_steps: string[];
}

export async function getFormSuggestions(form: Record<string, string>): Promise<FormFeedback> {
    // Convert form to array of entries to maintain order
    const formEntries = Object.entries(form);

    // Count filled fields (excluding instruction fields)
    const filledFieldsCount = formEntries.filter(([key, value]) => {
        // Skip instruction fields (they typically have encoded names or contain HTML)
        const isInstructionField = key.includes('instruction') ||
                                 key.includes('Advanced-instructions') ||
                                 value.includes('<ul') ||
                                 value.includes('<li') ||
                                 value.includes('<p');
        return !isInstructionField && value && value.trim() !== '';
    }).length;

    // Don't send to Gemini until at least 6 fields are filled
    if (filledFieldsCount < 6) {
        console.log(`Only ${filledFieldsCount} fields filled. Waiting for at least 6 before sending to Gemini.`);
        return {
            suggestions: [],
            overall_assessment: "Form is still being filled out. Continue gathering information.",
            next_steps: ["Continue filling out the form fields"]
        };
    }

    // Find the index of the last field that has been filled out
    let lastFilledIndex = -1;
    for (let i = formEntries.length - 1; i >= 0; i--) {
        const [key, value] = formEntries[i];
        // Skip instruction fields when determining progress
        const isInstructionField = key.includes('instruction') ||
                                 key.includes('Advanced-instructions') ||
                                 value.includes('<ul') ||
                                 value.includes('<li') ||
                                 value.includes('<p');
        if (!isInstructionField && value && value.trim() !== '') {
            lastFilledIndex = i;
            break;
        }
    }

    // Generate form data based on progress
    let formData: string;
    if (lastFilledIndex === -1) {
        // If no fields are filled, include only the first non-instruction field
        const firstNonInstructionEntry = formEntries.find(([key, value]) => {
            const isInstructionField = key.includes('instruction') ||
                                     key.includes('Advanced-instructions') ||
                                     value.includes('<ul') ||
                                     value.includes('<li') ||
                                     value.includes('<p');
            return !isInstructionField;
        });
        formData = firstNonInstructionEntry ? `${firstNonInstructionEntry[0]}: ${firstNonInstructionEntry[1] || '(empty)'}` : '';
    } else {
        // Include all fields up to and including the last filled field
        const relevantEntries = formEntries.slice(0, lastFilledIndex + 1);
        formData = relevantEntries
            .map(([key, value]) => {
                // Format instruction fields differently
                const isInstructionField = key.includes('instruction') ||
                                         key.includes('Advanced-instructions') ||
                                         value.includes('<ul') ||
                                         value.includes('<li') ||
                                         value.includes('<p');

                if (isInstructionField) {
                    // Extract text content from HTML and format as instruction
                    const textContent = value.replace(/<[^>]*>/g, ' ')
                                           .replace(/&nbsp;/g, ' ')
                                           .replace(/&quot;/g, '"')
                                           .replace(/&amp;/g, '&')
                                           .replace(/\s+/g, ' ')
                                           .trim();
                    return `[INSTRUCTION]: ${textContent}`;
                } else {
                    return `${key}: ${value || '(empty)'}`;
                }
            })
            .join('\n');
    }

    // Log the form data being sent to Gemini for debugging
    console.log('Form data sent to Gemini:', formData);
    console.log(`Filled fields count: ${filledFieldsCount}`);

    const currentDateTime = new Date().toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short'
    });

    const response = await openrouter.chat.completions.create({
        model: "google/gemini-2.5-flash-preview-05-20",
        messages: [
            {
                role: "system",
                content: `You are an AI assistant helping a legal representative during a client intake call.

                Current date and time: ${currentDateTime}

                Analyze the partially filled intake form and provide actionable suggestions to help the rep gather complete and accurate information. Focus on:

                1. Missing critical information that should be collected
                2. Follow-up questions to clarify or expand on provided information
                3. Important legal considerations or red flags
                4. Documentation that may be needed

                Be concise and practical - the rep is on a live call and needs quick, actionable guidance.`
            },
            {
                role: "user",
                content: `Current intake form data:
                
${formData}

Please analyze this intake information and provide suggestions for the legal representative. Do not recommend filling out fields that are going to be filled out later. The form is currently being worked on, so pay attention to the order of questions presented and avoid asking the user to fill out fields that they haven't reached yet. Only make a recommendation once fields that come after the field in question have been filled out.`
            }
        ],
        response_format: {
            type: "json_schema",
            json_schema: {
                name: "form_feedback",
                schema: {
                    type: "object",
                    properties: {
                        suggestions: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    field: {
                                        type: "string",
                                        description: "The form field this suggestion relates to"
                                    },
                                    suggestion: {
                                        type: "string",
                                        description: "The specific suggestion or question to ask"
                                    },
                                    priority: {
                                        type: "string",
                                        enum: ["high", "medium", "low"],
                                        description: "Priority level of this suggestion"
                                    },
                                    type: {
                                        type: "string",
                                        enum: ["missing_info", "follow_up_question", "legal_consideration", "documentation_needed"],
                                        description: "Type of suggestion"
                                    }
                                },
                                required: ["field", "suggestion", "priority", "type"]
                            }
                        },
                        overall_assessment: {
                            type: "string",
                            description: "Brief overall assessment of the intake progress"
                        },
                        next_steps: {
                            type: "array",
                            items: {
                                type: "string"
                            },
                            description: "Recommended next steps for the rep"
                        }
                    },
                    required: ["suggestions", "overall_assessment", "next_steps"]
                }
            }
        },
        temperature: 0.3
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
        throw new Error('No response from AI');
    }

    return JSON.parse(content) as FormFeedback;
}
